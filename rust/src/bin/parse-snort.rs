use std::collections::{
    HashMap,
    HashSet,
};
use indexmap::IndexMap;
use std::fs::{
    File,
    create_dir_all,
    remove_dir_all,
    remove_file,
};
use std::io::{
    <PERSON>ufRead,
    BufReader,
    BufWriter,
    Write,
};
use std::path::Path;
use std::process::Command;
use std::thread;
use std::time::Instant;

use chrono::{
    NaiveDate,
    NaiveTime,
};
use clap::Parser as ClapParser;
use dashmap::DashMap;
use itoa::Buffer as ItoaBuffer;  // fast integer to string
use num_format::{
    Locale,
    ToFormattedString,
};
use mysql::{
    OptsBuilder,
    Pool,
    prelude::Queryable,
};
use rayon::iter::{
    IntoParallelRefIterator,
    ParallelIterator,
};
use serde_json;

use eterna::rahavard::{
    save_log,
    sort_dict,
};

use eterna::utils_classes::{
    MYSQLConfig,
    MYSQLV<PERSON>ue,
    <PERSON><PERSON><PERSON>,
    SnortConfig,
    SnortParser,
};

use eterna::utils::{
    all_values_are_0,
    create_name_of_database,
    create_name_of_index,
    create_path_of_infile,
    evenly_sized_batches,
    get_no_of_infiles,
    hms_to_hourkey,
};

use eterna::utils_ip::{
    is_private,
};

use eterna::utils_parsers::{
    parse_ln,
    ConfigType,
};

// 8MB buffer for optimal I/O performance
const FILE_BUFFER_SIZE: usize = 8 * 1024 * 1024;

#[derive(ClapParser, Debug)]
#[command(author, version, about)]
struct Args {
    #[arg(long = "source-log")]
    source_log: String,
    // /FOO/BAR/BAZ/2025-06-13--Fri.log

    #[arg(long = "log-date")]
    log_date: String,
    // 2020-01-02

    #[arg(long = "host-name")]
    host_name: String,
    // custom-host-name

    #[arg(long = "command")]
    command: String,
    // parse-snort

    #[arg(long = "already-accomplished", num_args = 0..)]
    already_accomplished: Vec<String>,
    // [] OR ["Sensor-1", "Sensor-2", ...]

    #[arg(long = "sensor-list-of-names", num_args = 1..)]
    sensor_list_of_names: Vec<String>,
    // ["Sensor-1", "Sensor-2", "Sensor-3", ...]

    #[arg(long = "sensor-list-of-names-and-addresses", num_args = 1..)]
    sensor_list_of_names_and_addresses: Vec<String>,
    // ["Sensor-1", "***********", "Sensor-2", "***********", ...]

    #[arg(long = "sensor-dict-of-addresses-and-names")]
    sensor_dict_of_addresses_and_names: String,
    // "{\"***********\": \"Sensor-1\", \"***********\": \"Sensor-2\", ...}"

    #[arg(long = "force", num_args = 0)]
    #[arg(default_value_t = false)]
    force: bool,
    // true/false
}

fn trim_line(line: &mut String) {
    while line.ends_with('\n') || line.ends_with('\r') {
        line.pop();
    }
}

fn write_into_infile(
    start_of_chunk: usize,
    end_of_chunk: usize,
    infile_path: &str,
    infile_index: usize,
    no_of_infiles: usize,
    instance_rows: &[Vec<String>],
    terminated_by: &str,
    enclosed_by: &str,
    command: &str,
    host_name: &str,
    log_file: &str,
) {
    save_log(command, host_name, log_file, &format!("  writing into {} ({}/{}): {} -> {}", infile_path, infile_index, no_of_infiles, start_of_chunk.to_formatted_string(&Locale::en), end_of_chunk.to_formatted_string(&Locale::en)), true).unwrap();

    let file = match File::create(infile_path) {
        Ok(f) => f,
        Err(e) => {
            save_log(command, host_name, log_file, &format!("Error creating infile {}: {}", infile_path, e), true).unwrap();
            return;
        }
    };

    let mut writer = BufWriter::with_capacity(512 * 1024, file);  // 512 KB buffer
    let mut id_buf = ItoaBuffer::new();
    let mut output = Vec::with_capacity(1024 * instance_rows.len());  // preallocate large buffer

    let mut row_id = start_of_chunk;
    for instance_row in instance_rows.iter() {
        row_id += 1;

        // ('a', 'b', 'c') -> 1-*@*-a-*@*-b-*@*-c
        output.extend_from_slice(enclosed_by.as_bytes());
        output.extend_from_slice(id_buf.format(row_id).as_bytes());
        output.extend_from_slice(enclosed_by.as_bytes());
        //
        for cell in instance_row {
            output.extend_from_slice(terminated_by.as_bytes());
            output.extend_from_slice(enclosed_by.as_bytes());
            output.extend_from_slice(cell.as_bytes());
            output.extend_from_slice(enclosed_by.as_bytes());
        }
        //
        output.push(b'\n');

        // flush if buffer is getting too large
        if output.len() > 8 * 1024 * 1024 {
            if let Err(e) = writer.write_all(&output) {
                save_log(command, host_name, log_file, &format!("Error writing to {}: {}", infile_path, e), true).unwrap();
                return;
            }
            output.clear();
        }
    }

    // final flush
    if let Err(e) = writer.write_all(&output) {
        save_log(command, host_name, log_file, &format!("Error final write to {}: {}", infile_path, e), true).unwrap();
    }

    if let Err(e) = writer.flush() {
        save_log(command, host_name, log_file, &format!("Error flushing {}: {}", infile_path, e), true).unwrap();
    }
}

fn main() {
    let args = Args::parse();

    // string -> dict
    let sensor_dict_of_addresses_and_names: HashMap<String, String> =
        serde_json::from_str(&args.sensor_dict_of_addresses_and_names)
            .expect("Failed to parse sensor_dict_of_addresses_and_names");

    // list -> set for O(1) lookup
    let already_accomplished: HashSet<String> =
        args.already_accomplished.into_iter().collect();

    // create dictionary of instances
    let sensor_names_and_instances: DashMap<String, SnortParser> = DashMap::new();
    for s_n in &args.sensor_list_of_names {
        sensor_names_and_instances.insert(
            s_n.clone(),
            SnortParser::new(
                SnortConfig::SLUG.value_string(),
                args.log_date.to_string(),
                s_n.to_string(),
            ),
        );
    }

    println!("parsing...");
    let parse_start = Instant::now();

    // open file with larger buffer for better I/O performance
    let file = File::open(&args.source_log)
        .expect(&format!("Failed to open source log: {}", args.source_log));
    let mut reader = BufReader::with_capacity(FILE_BUFFER_SIZE, file);

    // process file in chunks to avoid loading entire file into memory
    let pool_chunksize = if let eterna::utils_classes::MYSQLValue::Int(size) = MYSQLConfig::POOL_CHUNKSIZE.value() {
        size
    } else {
        panic!("Error getting pool_chunksize from MYSQLConfig");
    };

    loop {
        let mut chunk = Vec::with_capacity(pool_chunksize);

        // read chunk of lines
        for _ in 0..pool_chunksize {
            let mut line = String::new();
            match reader.read_line(&mut line) {
                Ok(0) => break,  // EOF
                Ok(_) => {
                    trim_line(&mut line);  // remove newline character
                    chunk.push(line);
                }
                Err(e) => panic!("Error reading line: {}", e),
            }
        }

        if chunk.is_empty() {
            // EOF reached
            break;
        }

        // process chunk in parallel, aggregate locally to avoid DashMap contention
        let local_results: HashMap<String, Vec<Vec<String>>> = chunk
            .par_iter()
            .map(|line| {
                let (sensor_name, parsed_ln) = parse_ln(
                    line.trim(),
                    ConfigType::Snort,
                    &args.sensor_list_of_names_and_addresses,
                    &sensor_dict_of_addresses_and_names,
                );

                // check if sensor is already accomplished (O(1) lookup)
                if let Some(ref name) = sensor_name {
                    if already_accomplished.contains(name) {
                        return None;
                    }
                }

                match (sensor_name, parsed_ln) {
                    (Some(name), Some(row)) => Some((name, row)),
                    _ => None,
                }
            })
            .filter_map(|x| x)
            .fold(HashMap::new, |mut acc, (name, row)| {
                acc.entry(name).or_insert_with(Vec::new).push(row);
                acc
            })
            .reduce(HashMap::new, |mut acc1, acc2| {
                for (k, mut v) in acc2 {
                    acc1.entry(k).or_insert_with(Vec::new).append(&mut v);
                }
                acc1
            });

        // collect results into sensor instances
        for (name, rows) in local_results {
            if let Some(mut instance) = sensor_names_and_instances.get_mut(&name) {
                instance.rows.extend(rows);
            }
        }
    }

    let parse_duration = parse_start.elapsed();
    println!("parsed in {} seconds", parse_duration.as_secs().to_formatted_string(&Locale::en));


    for entry in sensor_names_and_instances.iter() {
        let sensor_start = Instant::now();

        let sensor_name = entry.key();
        let mut instance = entry.value().clone();

        let dest_dir          = format!("{}/{}/{}", SnortConfig::get_logs_parsed_dir(), sensor_name, args.log_date);
        let accomplished_file = format!("{}/{}-accomplished.log", dest_dir, args.log_date);
        let log_file          = format!("{}/{}.log", dest_dir, args.log_date);

        let database_name = create_name_of_database(&SnortConfig::SLUG.value_string(), &args.log_date, sensor_name);

        // ################################################

        // remove and/or create dest_dir
        if Path::new(&dest_dir).exists() {
            let mut should_rm_dest_dir = false;

            if args.force {
                should_rm_dest_dir = true;
            } else {
                if Path::new(&accomplished_file).exists() {
                    println!("{} for sensor {} is already parsed. skipping", args.log_date, sensor_name);
                    continue;
                } else {
                    should_rm_dest_dir = true;
                }
            };

            if should_rm_dest_dir {
                println!("removing {}", dest_dir);
                if let Err(e) = remove_dir_all(&dest_dir) {
                    save_log(&args.command, &args.host_name, &log_file, &format!("Error removing directory {}: {}", dest_dir, e), true).unwrap();
                }
                println!("creating {}", dest_dir);
                if let Err(e) = create_dir_all(&dest_dir) {
                    save_log(&args.command, &args.host_name, &log_file, &format!("Error creating directory {}: {}", dest_dir, e), true).unwrap();
                }
            }
        } else {
            println!("creating {}", dest_dir);
            if let Err(e) = create_dir_all(&dest_dir) {
                save_log(&args.command, &args.host_name, &log_file, &format!("Error creating directory {}: {}", dest_dir, e), true).unwrap();
            }
        }

        // ################################################

        // START __inserting_into_dbs__

        let mysql_host = match MYSQLConfig::MYSQL_HOST.value() {
            MYSQLValue::Str(host) => host,
            _ => panic!("Error getting MYSQL_HOST"),
        };
        let mysql_user = match MYSQLConfig::MYSQL_MASTER.value() {
            MYSQLValue::Str(user) => user,
            _ => panic!("Error getting MYSQL_MASTER"),
        };
        let mysql_password = match MYSQLConfig::MYSQL_MASTER_PASSWD.value() {
            MYSQLValue::Str(password) => password,
            _ => panic!("Error getting MYSQL_MASTER_PASSWD"),
        };

        let db_opts = OptsBuilder::new()
            .ip_or_hostname(Some(mysql_host.clone()))
            .user(Some(mysql_user.clone()))
            .pass(Some(mysql_password.clone()));

        // drop/create database
        match Pool::new(db_opts) {
            Ok(pool) => {
                match pool.get_conn() {
                    Ok(mut conn) => {
                        save_log(&args.command, &args.host_name, &log_file, &format!("dropping database {}", database_name), true).unwrap();
                        if let Err(e) = conn.query_drop(format!("DROP DATABASE IF EXISTS {};", database_name)) {
                            save_log(&args.command, &args.host_name, &log_file, &format!("Error dropping database {}: {}", database_name, e), true).unwrap();
                        }

                        save_log(&args.command, &args.host_name, &log_file, &format!("creating database {}", database_name), true).unwrap();
                        if let Err(e) = conn.query_drop(format!("CREATE DATABASE {};", database_name)) {
                            save_log(&args.command, &args.host_name, &log_file, &format!("Error creating database {}: {}", database_name, e), true).unwrap();
                        }
                    }
                    Err(e) => {
                        save_log(&args.command, &args.host_name, &log_file, &format!("Error getting database connection: {}", e), true).unwrap();
                    }
                }
            }
            Err(e) => {
                save_log(&args.command, &args.host_name, &log_file, &format!("Error creating database pool: {}", e), true).unwrap();
            }
        }

        // ################################################
        // *table

        // __CHUNKED_INFILE__

        let no_of_infiles = get_no_of_infiles(instance.no_of_rows());

        if no_of_infiles > 0 {
            save_log(&args.command, &args.host_name, &log_file, &format!("{} rows will be inserted into database", instance.no_of_rows().to_formatted_string(&Locale::en)), true).unwrap();

            let db_opts_with_db = OptsBuilder::new()
            .ip_or_hostname(Some(mysql_host.clone()))
            .user(Some(mysql_user.clone()))
            .pass(Some(mysql_password.clone()))
            .db_name(Some(database_name.clone()));

            // create table and insert data
            match Pool::new(db_opts_with_db) {
                Ok(pool) => {
                    match pool.get_conn() {
                        Ok(mut conn) => {
                            let table_name = SnortConfig::get_table_name();
                            save_log(&args.command, &args.host_name, &log_file, &format!("creating table {}", table_name), true).unwrap();

                            let db_columns = match SnortConfig::DB_COLUMNS.value() {
                                MYSQLValue::Str(cols) => cols,
                                _ => panic!("Error getting DB_COLUMNS"),
                            };

                            if let Err(e) = conn.query_drop(format!("CREATE TABLE {} ({});", table_name, db_columns)) {
                                save_log(&args.command, &args.host_name, &log_file, &format!("Error creating table {}: {}", table_name, e), true).unwrap();
                                return;
                            }

                            if let Err(e) = conn.query_drop("SET UNIQUE_CHECKS=0;") {
                                save_log(&args.command, &args.host_name, &log_file, &format!("Error for UNIQUE_CHECKS=0: {}", e), true).unwrap();
                            }
                            if let Err(e) = conn.query_drop("SET FOREIGN_KEY_CHECKS=0;") {
                                save_log(&args.command, &args.host_name, &log_file, &format!("Error for FOREIGN_KEY_CHECKS=0: {}", e), true).unwrap();
                            }
                            if let Err(e) = conn.query_drop("START TRANSACTION;") {
                                save_log(&args.command, &args.host_name, &log_file, &format!("Error for START TRANSACTION: {}", e), true).unwrap();
                            }

                            save_log(&args.command, &args.host_name, &log_file, &format!("{} infiles will be created", no_of_infiles), true).unwrap();

                            let infile_chunksize = match MYSQLConfig::INFILE_CHUNKSIZE.value() {
                                MYSQLValue::Int(size) => size,
                                _ => panic!("Error getting INFILE_CHUNKSIZE"),
                            };

                            // process infiles in batches
                            let batches = evenly_sized_batches(no_of_infiles as isize, None);

                            for (batch_index, batch) in batches.iter().enumerate() {
                                let batch_index = batch_index + 1;
                                save_log(&args.command, &args.host_name, &log_file, &format!("batch {}: writing into {} infiles", batch_index, batch.len()), true).unwrap();

                                let mut handles = vec![];
                                let mut infile_paths = vec![];

                                // STEP 1: create n infiles at the same time
                                for &infile_index in batch {
                                    let infile_path = create_path_of_infile(&database_name, &table_name, Some(infile_index));
                                    let start_of_chunk = infile_chunksize * (infile_index - 1);
                                    let end_of_chunk = start_of_chunk + infile_chunksize;

                                    infile_paths.push(infile_path.clone());


                                    // calculating end_of_chunk__real to avoid the error:
                                    //   thread 'main' panicked at src/bin/parse-*.rs:421:77:
                                    //   range end index 5000000 out of range for slice of length 1121362
                                    let end_of_chunk__real = usize::min(start_of_chunk + infile_chunksize, instance.no_of_rows());
                                    //
                                    // only borrow the exact slice this thread will need
                                    let instance_rows_slice = &instance.rows[start_of_chunk..end_of_chunk__real];
                                    let instance_rows_vec = instance_rows_slice.to_vec(); // ✅ small clone

                                    let terminated_by = match MYSQLConfig::TERMINATED_BY.value() {
                                        MYSQLValue::Str(term) => term,
                                        _ => panic!("Error getting TERMINATED_BY"),
                                    };
                                    let enclosed_by = match MYSQLConfig::ENCLOSED_BY.value() {
                                        MYSQLValue::Str(enc) => enc,
                                        _ => panic!("Error getting ENCLOSED_BY"),
                                    };
                                    let command_clone   = args.command.clone();
                                    let host_name_clone = args.host_name.clone();
                                    let log_file_clone  = log_file.clone();


                                    let handle = thread::spawn(move || {
                                        write_into_infile(
                                            start_of_chunk,
                                            instance_rows_vec.len(),  // passed as end_of_chunk in python
                                            &infile_path,
                                            infile_index,
                                            no_of_infiles,
                                            &instance_rows_vec,
                                            &terminated_by,
                                            &enclosed_by,
                                            &command_clone,
                                            &host_name_clone,
                                            &log_file_clone,
                                        );
                                    });

                                    handles.push(handle);
                                }

                                // wait for all threads to complete
                                for handle in handles {
                                    if let Err(e) = handle.join() {
                                        save_log(&args.command, &args.host_name, &log_file, &format!("Thread panicked: {:?}", e), true).unwrap();
                                    }
                                }

                                // STEP 2: insert the n infiles into database one at a time
                                save_log(&args.command, &args.host_name, &log_file, &format!("batch {}: inserting into {} from {} infiles", batch_index, table_name, infile_paths.len()), true).unwrap();

                                for (infile_idx, infile_path) in infile_paths.iter().enumerate() {
                                    if !Path::new(infile_path).exists() {
                                        continue;
                                    }

                                    save_log(&args.command, &args.host_name, &log_file, &format!("  inserting from {}", infile_path), true).unwrap();

                                    let terminated_by = match MYSQLConfig::TERMINATED_BY.value() {
                                        MYSQLValue::Str(term) => term,
                                        _ => panic!("Error getting TERMINATED_BY"),
                                    };
                                    let enclosed_by = match MYSQLConfig::ENCLOSED_BY.value() {
                                        MYSQLValue::Str(enc) => enc,
                                        _ => panic!("Error getting ENCLOSED_BY"),
                                    };
                                    let db_keys = match SnortConfig::DB_KEYS.value() {
                                        MYSQLValue::Str(keys) => keys,
                                        _ => panic!("Error getting DB_KEYS"),
                                    };

                                    let infile_statement = format!(
                                        r#"{} "{}"
                                        INTO TABLE {}
                                        FIELDS TERMINATED BY "{}"
                                        ENCLOSED BY '{}'
                                        LINES TERMINATED BY "\n"
                                        (ID,{});"#,
                                        MYSQLConfig::get_infile_statement(),
                                        infile_path,
                                        table_name,
                                        terminated_by,
                                        enclosed_by,
                                        db_keys
                                    );

                                    // JUMP_1
                                    // if let Err(e) = conn.query_drop(infile_statement) {
                                    //     save_log(&args.command, &args.host_name, &log_file, &format!("Error executing infile statement: {}", e), true).unwrap();
                                    // }

                                    // NOTE using shell command instead of JUMP_1
                                    //      because mysql ≥ 25.0 (set in Cargo.toml)
                                    //      has dropped support for "LOAD DATA LOCAL INFILE" entirely
                                    let infile_status = Command::new("mysql")
                                        .args([
                                            "-u", &mysql_user,
                                            &format!("-p{}", mysql_password),

                                            // for using infiles located in /tmp.
                                            // no harm to be on development.
                                            // __TODO__ check if required in production
                                            "--local-infile=1",

                                            "-e", &infile_statement,
                                            &database_name,
                                        ])
                                        .status();
                                    //
                                    match infile_status {
                                        Ok(exit_status) => {
                                            if !exit_status.success() {
                                                save_log(&args.command, &args.host_name, &log_file, &format!("Infile statement failed for: {}", infile_path), true).unwrap();
                                            }
                                        }
                                        Err(e) => {
                                            save_log(&args.command, &args.host_name, &log_file, &format!("Error executing infile statement: {}", e), true).unwrap();
                                        }
                                    }
                                }

                                // commit after loop
                                save_log(&args.command, &args.host_name, &log_file, "  committing...", true).unwrap();
                                if let Err(e) = conn.query_drop("COMMIT;") {
                                    save_log(&args.command, &args.host_name, &log_file, &format!("Error committing: {}", e), true).unwrap();
                                }

                                for infile_path in &infile_paths {
                                    // remove infile
                                    save_log(&args.command, &args.host_name, &log_file, &format!("  removing {}", infile_path), true).unwrap();
                                    if let Err(e) = remove_file(infile_path) {
                                        save_log(&args.command, &args.host_name, &log_file, &format!("Error removing {}: {}", infile_path, e), true).unwrap();
                                    }
                                }
                            }

                            let tablenames_and_keys_for_index = match SnortConfig::TABLENAMES_AND_KEYS_FOR_INDEX.value() {
                                MYSQLValue::Tuple(tuples) => tuples,
                                _ => panic!("Error getting TABLENAMES_AND_KEYS_FOR_INDEX"),
                            };

                            // __CREATE_INDEX__
                            // could not add IF NOT EXISTS
                            // because on remote it is not valid
                            // as of mysql 8.4.0 and throws error.
                            // for that, we have to use try block
                            // in order to avoid the error:
                            // OperationalError(1061, "Duplicate key name '<NAME_OF_INDEX>'")
                            for (t_n_, ky_) in tablenames_and_keys_for_index {
                                let ky_ = match ky_ {
                                    eterna::utils_classes::StrAndInt::Str(s) => s,
                                    eterna::utils_classes::StrAndInt::Int(i) => i.to_string(),
                                };

                                let index_name = create_name_of_index(&ky_);
                                save_log(&args.command, &args.host_name, &log_file, &format!("creating index {}", index_name), true).unwrap();

                                let index_prefix_length = match MYSQLConfig::INDEX_PREFIX_LENGTH.value() {
                                    MYSQLValue::Int(len) => len,
                                    _ => panic!("Error getting INDEX_PREFIX_LENGTH"),
                                };
                                let index_type = match MYSQLConfig::INDEX_TYPE.value() {
                                    MYSQLValue::Str(typ) => typ,
                                    _ => panic!("Error getting INDEX_TYPE"),
                                };

                                let create_index_statement = format!(
                                    "CREATE INDEX {} ON {} ({}({})) USING {};",
                                    index_name,
                                    t_n_,
                                    ky_,
                                    index_prefix_length,
                                    index_type
                                );

                                match conn.query_drop(&create_index_statement) {
                                    Ok(_) => {
                                        if let Err(e) = conn.query_drop("COMMIT;") {
                                            save_log(&args.command, &args.host_name, &log_file, &format!("Error committing index creation: {}", e), true).unwrap();
                                        }
                                    }
                                    Err(e) => {
                                        save_log(&args.command, &args.host_name, &log_file, &format!("Error creating index: {:?}", e), true).unwrap();
                                    }
                                }
                            }
                        }
                        Err(e) => {
                            save_log(&args.command, &args.host_name, &log_file, &format!("Error getting database connection: {}", e), true).unwrap();
                        }
                    }
                }
                Err(e) => {
                    save_log(&args.command, &args.host_name, &log_file, &format!("Error creating database pool: {}", e), true).unwrap();
                }
            }
        }

        // ################################################
        // *_and_counts

        // __INDEXES_ONE_OFF__
        // the indexes in parse-<APP_SLUG>.py (this script) are lower by 1
        // compared to its hourly counterpart (i.e. hourly-parse-<APP_SLUG>.py).
        // in hourly-parse-<APP_SLUG>.py,
        // instance.rows are directlry read from *table in database
        // meaning ID column is also included
        // so we have to increment indexes by 1
        // to get the right columns

        save_log(&args.command, &args.host_name, &log_file, "preparing *_and_counts", true).unwrap();

        let mut hms_counts: HashMap<String, i32> = HashMap::new();

        for row in &instance.rows {
            let hms = &row[1];
            *hms_counts.entry(hms.clone()).or_insert(0) += 1;

            *instance.gidsids_and_counts.entry(row[2].clone()).or_insert(0) += 1;
            *instance.descriptions_and_counts.entry(row[3].clone()).or_insert(0) += 1;
            *instance.classifications_and_counts.entry(row[4].clone()).or_insert(0) += 1;
            *instance.priorities_and_counts.entry(row[5].clone()).or_insert(0) += 1;
            *instance.protocols_and_counts.entry(row[6].clone()).or_insert(0) += 1;
            *instance.source_ips_and_counts.entry(row[7].clone()).or_insert(0) += 1;
            *instance.source_ports_and_counts.entry(row[8].clone()).or_insert(0) += 1;
            *instance.destination_ips_and_counts.entry(row[9].clone()).or_insert(0) += 1;
            *instance.destination_ports_and_counts.entry(row[10].clone()).or_insert(0) += 1;
        }

        if let Ok(base_date) = chrono::NaiveDate::parse_from_str(&args.log_date, "%Y-%m-%d") {
            for (hms, count) in hms_counts {
                // {'00:49:51': 12, '02:59:55': 1182, ...}
                // ->
                // {'00:00 - 00:59': 416787, '01:00 - 01:59': 416167, ...}
                let hour_key = hms_to_hourkey(&hms);
                *instance.times_and_counts.entry(hour_key).or_insert(0) += count;

                // -----

                // log_date hms -> millisecond
                // (2023-05-12 00:00:26 -> 1624973400000)
                if let Ok(time) = NaiveTime::parse_from_str(&hms, "%H:%M:%S") {
                    let datetime = base_date.and_time(time);
                    let milliseconds = datetime.and_utc().timestamp_millis();

                    // convert to string
                    let mut buffer_ = itoa::Buffer::new();
                    let milliseconds__str = buffer_.format(milliseconds);

                    instance.milliseconds_and_counts.insert(milliseconds__str.to_owned(), count);
                }
            }
        } else {
            // fallback: base_date not valid. only update times_and_counts
            for (hms, count) in hms_counts {
                let hour_key = hms_to_hourkey(&hms);
                *instance.times_and_counts.entry(hour_key).or_insert(0) += count;
            }
        }

        // __TODO__ necessary ?
        // in-place update of the instance
        // save_log(&args.command, &args.host_name, &log_file, "updating sensor_names_and_instances", true).unwrap();
        // if let Some(mut entry) = sensor_names_and_instances.get_mut(sensor_name.as_str()) {
        //     // let instance = entry.value().clone();
        //     *entry = instance;
        // }

        // ################################################

        instance.levels_and_counts.insert("Critical".to_string(), 0);
        instance.levels_and_counts.insert("Warning".to_string(),  0);
        instance.levels_and_counts.insert("Low".to_string(),      0);
        instance.levels_and_counts.insert("Very Low".to_string(), 0);

        let classifications__criticals = match SnortConfig::CLASSIFICATIONS__CRITICALS.value() {
            MYSQLValue::List(list) => list,
            _ => panic!("Error getting CLASSIFICATIONS__CRITICALS"),
        };
        let classifications__warnings = match SnortConfig::CLASSIFICATIONS__WARNINGS.value() {
            MYSQLValue::List(list) => list,
            _ => panic!("Error getting CLASSIFICATIONS__WARNINGS"),
        };
        let classifications__lows = match SnortConfig::CLASSIFICATIONS__LOWS.value() {
            MYSQLValue::List(list) => list,
            _ => panic!("Error getting CLASSIFICATIONS__LOWS"),
        };
        let classifications__very_lows = match SnortConfig::CLASSIFICATIONS__VERY_LOWS.value() {
            MYSQLValue::List(list) => list,
            _ => panic!("Error getting CLASSIFICATIONS__VERY_LOWS"),
        };

        for (classification_, count_) in &instance.classifications_and_counts {
            if classifications__criticals.contains(classification_) {
                *instance.levels_and_counts.get_mut("Critical").unwrap() += count_;
            } else if classifications__warnings.contains(classification_) {
                *instance.levels_and_counts.get_mut("Warning").unwrap() += count_;
            } else if classifications__lows.contains(classification_) {
                *instance.levels_and_counts.get_mut("Low").unwrap() += count_;
            } else if classifications__very_lows.contains(classification_) {
                *instance.levels_and_counts.get_mut("Very Low").unwrap() += count_;
            }
        }

        // ################################################
        // *toptable

        let db_opts_with_db = OptsBuilder::new()
            .ip_or_hostname(Some(mysql_host.clone()))
            .user(Some(mysql_user.clone()))
            .pass(Some(mysql_password.clone()))
            .db_name(Some(database_name.clone()));

        match Pool::new(db_opts_with_db) {
            Ok(pool) => {
                match pool.get_conn() {
                    Ok(mut conn) => {
                        let table_configs = vec![
                            // dictionary                            table_name                 column/key
                            (&instance.times_and_counts,             "timetoptable",            "Time"),
                            (&instance.gidsids_and_counts,           "gidsidtoptable",          "`GID:SID`"),
                            (&instance.descriptions_and_counts,      "descriptiontoptable",     "Description"),
                            (&instance.classifications_and_counts,   "classificationtoptable",  "Classification"),
                            (&instance.priorities_and_counts,        "prioritytoptable",        "Priority"),
                            (&instance.protocols_and_counts,         "protocoltoptable",        "Protocol"),
                            (&instance.source_ips_and_counts,        "sourceiptoptable",        "`Source IP`"),
                            (&instance.source_ports_and_counts,      "sourceporttoptable",      "`Source Port`"),
                            (&instance.destination_ips_and_counts,   "destinationiptoptable",   "`Destination IP`"),
                            (&instance.destination_ports_and_counts, "destinationporttoptable", "`Destination Port`"),

                            (&instance.milliseconds_and_counts,      "millisecondtoptable",     "Millisecond"),
                            (&instance.levels_and_counts,            "leveltoptable",           "Level"),
                        ];

                        for (dictionary, table_name, key) in table_configs {
                            let mut dict_to_use = dictionary.clone();

                            if (key == "Time" || key == "Millisecond") && all_values_are_0(&dict_to_use) {
                                dict_to_use.clear();
                            }

                            if dict_to_use.is_empty() {
                                continue;
                            }

                            let sorted_dict = if key == "Time" || key == "Millisecond" {
                                sort_dict(&dict_to_use, "key", false)
                            } else {
                                sort_dict(&dict_to_use, "value", true)
                            };

                            let id_data_type = match MYSQLConfig::ID_DATA_TYPE.value() {
                                MYSQLValue::Str(val) => val,
                                _ => panic!("Error getting ID_DATA_TYPE"),
                            };
                            let default_data_type = match MYSQLConfig::DEFAULT_DATA_TYPE.value() {
                                MYSQLValue::Str(val) => val,
                                _ => panic!("Error getting DEFAULT_DATA_TYPE"),
                            };
                            let count_data_type = match MYSQLConfig::COUNT_DATA_TYPE.value() {
                                MYSQLValue::Str(val) => val,
                                _ => panic!("Error getting COUNT_DATA_TYPE"),
                            };

                            let table_columns = format!(
                                "ID {}, {} {}, Count {}",
                                id_data_type,
                                key,
                                default_data_type,
                                count_data_type,
                            );
                            let table_keys = format!("{},Count", key);
                            let table_marks = "?,?";

                            save_log(&args.command, &args.host_name, &log_file, &format!("creating table {}", table_name), true).unwrap();
                            if let Err(e) = conn.query_drop(format!("CREATE TABLE {} ({});", table_name, table_columns)) {
                                save_log(&args.command, &args.host_name, &log_file, &format!("Error creating table {}: {}", table_name, e), true).unwrap();
                                continue;
                            }

                            save_log(&args.command, &args.host_name, &log_file, &format!("inserting {} rows into {}", sorted_dict.len().to_formatted_string(&Locale::en), table_name), true).unwrap();
                            if let Err(e) = conn.query_drop("START TRANSACTION;") {
                                save_log(&args.command, &args.host_name, &log_file, &format!("Error starting transaction: {}", e), true).unwrap();
                                continue;
                            }

                            for (k, v) in sorted_dict.iter() {
                                if let Err(e) = conn.exec_drop(&format!("INSERT INTO {} ({}) VALUES ({});", table_name, table_keys, table_marks), (k, v)) {
                                    save_log(&args.command, &args.host_name, &log_file, &format!("Error inserting into {}: {}", table_name, e), true).unwrap();
                                    break;
                                }
                            }
                        }

                        if let Err(e) = conn.query_drop("COMMIT;") {
                            save_log(&args.command, &args.host_name, &log_file, &format!("Error committing: {}", e), true).unwrap();
                        }
                    }
                    Err(e) => {
                        save_log(&args.command, &args.host_name, &log_file, &format!("Error getting database connection: {}", e), true).unwrap();
                    }
                }
            }
            Err(e) => {
                save_log(&args.command, &args.host_name, &log_file, &format!("Error creating database pool: {}", e), true).unwrap();
            }
        }

        // ################################################
        save_log(&args.command, &args.host_name, &log_file, "1", true).unwrap();

        // __INDEXES_ONE_OFF__
        // Extract source IPs and destination IPs from instance.rows
        // Index 7 = source IP, Index 9 = destination IP
        let src_ips_and_dest_ips: Vec<(String, String)> = instance.rows
            .iter()
            .map(|row| (row[7].clone(), row[9].clone()))
            .collect();

        save_log(&args.command, &args.host_name, &log_file, "2", true).unwrap();

        let mut dest_ips_and_src_ips__dict: HashMap<String, Vec<String>> = HashMap::new();
        for (src_ip, dest_ip) in src_ips_and_dest_ips {
            if is_private(&dest_ip) {
                continue;
            }

            // Note: commented in Python because on some servers
            // even when destination ips are public, source ips may be public ips too
            // if !is_private(&src_ip) {
            //     continue;
            // }

            dest_ips_and_src_ips__dict
                .entry(dest_ip)
                .or_insert_with(Vec::new)
                .push(src_ip);
        }

        save_log(&args.command, &args.host_name, &log_file, "3", true).unwrap();

        let mut dest_ips_and_src_ips_counts: HashMap<String, HashMap<String, i32>> = HashMap::new();
        for (dest_ip, src_ips) in dest_ips_and_src_ips__dict {
            let mut src_ip_counts: HashMap<String, i32> = HashMap::new();
            for src_ip in src_ips {
                *src_ip_counts.entry(src_ip).or_insert(0) += 1;
            }

            // Sort by count (value) in descending order and convert back to HashMap for JSON serialization
            let sorted_src_ip_counts = sort_dict(&src_ip_counts, "value", true);
            let src_ip_counts_hashmap: HashMap<String, i32> = sorted_src_ip_counts.into_iter().collect();
            dest_ips_and_src_ips_counts.insert(dest_ip, src_ip_counts_hashmap);
        }

        save_log(&args.command, &args.host_name, &log_file, "4", true).unwrap();
        
        // Sort by destination IP (key) in ascending order
        // Since we can't use sort_dict with HashMap values, we'll sort manually
        let mut dest_ips_sorted: Vec<_> = dest_ips_and_src_ips_counts.into_iter().collect();
        dest_ips_sorted.sort_by(|a, b| a.0.cmp(&b.0)); // Sort by destination IP (key)
        let dest_ips_and_src_ips_counts: IndexMap<String, HashMap<String, i32>> = dest_ips_sorted.into_iter().collect();
        
        save_log(&args.command, &args.host_name, &log_file, "5", true).unwrap();

        let id_data_type = match MYSQLConfig::ID_DATA_TYPE.value() {
            MYSQLValue::Str(val) => val,
            _ => panic!("Error getting ID_DATA_TYPE"),
        };
        let default_data_type = match MYSQLConfig::DEFAULT_DATA_TYPE.value() {
            MYSQLValue::Str(val) => val,
            _ => panic!("Error getting DEFAULT_DATA_TYPE"),
        };
        let count_data_type = match MYSQLConfig::COUNT_DATA_TYPE.value() {
            MYSQLValue::Str(val) => val,
            _ => panic!("Error getting COUNT_DATA_TYPE"),
        };

        // IP        column contains destination ips
        // IPsCounts column contains source ips + counts
        let table_name = "visitorsofiptable";
        let table_columns = format!(
            "ID          {},
             IP          {},
             IPsCounts   {},
             `No of IPs` {}",
            id_data_type,
            default_data_type,
            default_data_type,
            count_data_type,
        );
        let table_keys = "IP,IPsCounts,`No of IPs`";
        let table_marks = "?,?,?";

        let db_opts_for_visitors = OptsBuilder::new()
            .ip_or_hostname(Some(mysql_host.clone()))
            .user(Some(mysql_user.clone()))
            .pass(Some(mysql_password.clone()))
            .db_name(Some(database_name.clone()));

        match Pool::new(db_opts_for_visitors) {
            Ok(pool) => {
                match pool.get_conn() {
                    Ok(mut conn) => {
                        save_log(&args.command, &args.host_name, &log_file, &format!("creating table {}", table_name), true).unwrap();
                        if let Err(e) = conn.query_drop(format!("CREATE TABLE {} ({});", table_name, table_columns)) {
                            save_log(&args.command, &args.host_name, &log_file, &format!("Error creating table {}: {}", table_name, e), true).unwrap();
                        } else {
                            save_log(&args.command, &args.host_name, &log_file, &format!("inserting {} rows into {}", dest_ips_and_src_ips_counts.len().to_formatted_string(&Locale::en), table_name), true).unwrap();

                            if let Err(e) = conn.query_drop("START TRANSACTION;") {
                                save_log(&args.command, &args.host_name, &log_file, &format!("Error starting transaction: {}", e), true).unwrap();
                            } else {
                                for (dest_ip, src_ips_counts) in dest_ips_and_src_ips_counts.iter() {
                                    // Convert HashMap to JSON string (equivalent to Python's dumps(v))
                                    let ips_counts_json = match serde_json::to_string(src_ips_counts) {
                                        Ok(json) => json,
                                        Err(e) => {
                                            save_log(&args.command, &args.host_name, &log_file, &format!("Error serializing IP counts: {}", e), true).unwrap();
                                            continue;
                                        }
                                    };

                                    let no_of_ips = src_ips_counts.len() as i32;

                                    if let Err(e) = conn.exec_drop(
                                        &format!("INSERT INTO {} ({}) VALUES ({});", table_name, table_keys, table_marks),
                                        (dest_ip, &ips_counts_json, no_of_ips)
                                    ) {
                                        save_log(&args.command, &args.host_name, &log_file, &format!("Error inserting into {}: {}", table_name, e), true).unwrap();
                                        break;
                                    }
                                }

                                if let Err(e) = conn.query_drop("COMMIT;") {
                                    save_log(&args.command, &args.host_name, &log_file, &format!("Error committing: {}", e), true).unwrap();
                                }
                            }
                        }
                    }
                    Err(e) => {
                        save_log(&args.command, &args.host_name, &log_file, &format!("Error getting database connection: {}", e), true).unwrap();
                    }
                }
            }
            Err(e) => {
                save_log(&args.command, &args.host_name, &log_file, &format!("Error creating database pool: {}", e), true).unwrap();
            }
        }

        // ################################################
        instance.truncate_all();
        // ################################################

        // __TODO__
        // database info
        // tables info

        // ################################################

        // create accomplished_file
        save_log(&args.command, &args.host_name, &accomplished_file, "accomplished", true).unwrap();

        let sensor_duration = sensor_start.elapsed();
        save_log(&args.command, &args.host_name, &log_file, &format!("accomplished in {} seconds", sensor_duration.as_secs().to_formatted_string(&Locale::en)), true).unwrap();

        // END __inserting_into_dbs__
    }
}
